"use client";

import { useUserStore } from "@/lib/store/useUserStore";
import {
  SessionProvider as NextSessionProvider,
  useSession,
} from "next-auth/react";
import { useEffect } from "react";

export function SessionProvider({ children }: { children: React.ReactNode }) {
  const { session } = useSession();
  const { setUser } = useUserStore();

  useEffect(() => {
    if (session?.user) {
      // Fetch user data from API when session changes
      const fetchUserData = async () => {
        try {
          const response = await fetch("/api/user");
          if (response.ok) {
            const userData = await response.json();
            setUser(userData);
          }
        } catch (error) {
          console.error("Failed to fetch user ", error);
        }
      };

      fetchUserData();
    } else {
      setUser(null);
    }
  }, [session, setUser]);

  return <NextSessionProvider>{children}</NextSessionProvider>;
}
