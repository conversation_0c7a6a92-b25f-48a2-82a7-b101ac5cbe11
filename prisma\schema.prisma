// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DATABASE_URL")
}

model User {
  id            String         @id @default(cuid())
  name          String?
  email         String?        @unique
  emailVerified DateTime?
  image         String?
  role          String         @default("USER")
  balance       Int            @default(0)
  purchases     Purchase[]
  videos        Video[]        @relation("CreatorVideos")
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  Account       Account[]
  Session       Session[]
  Subscription  Subscription[]
  Like          Like[]
  Comment       Comment[]
  Notification  Notification[]
  Subscriber    Subscription[]
  Subscriptions Subscription[]
  Subscription  Subscription[]
  Subscription  Subscription[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Video {
  id          String     @id @default(cuid())
  title       String
  description String?
  url         String
  thumbnail   String?
  price       Int
  creatorId   String
  creator     User       @relation(name: "CreatorVideos", fields: [creatorId], references: [id])
  purchases   Purchase[]
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  Like        Like[]
  Comment     Comment[]

  @@map("videos")
}

model Subscription {
  id        String   @id @default(cuid())
  userId    String
  creatorId String
  startDate DateTime @default(now())
  endDate   DateTime
  isActive  Boolean  @default(true)
  user      User     @relation(name: "subscriptions", fields: [userId], references: [id])
  creator   User     @relation(name: "subscribers", fields: [creatorId], references: [id])
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  User      User     @relation(fields: [userId], references: [id])

  @@map("subscriptions")
}

model Purchase {
  id        String   @id @default(cuid())
  userId    String
  videoId   String
  amount    Int
  user      User     @relation(fields: [userId], references: [id])
  video     Video    @relation(fields: [videoId], references: [id])
  createdAt DateTime @default(now())

  @@map("purchases")
}

model Like {
  id        String   @id @default(cuid())
  userId    String
  videoId   String
  type      String   @default("like") // "like" or "dislike"
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  video     Video    @relation(fields: [videoId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now())

  @@unique([userId, videoId])
  @@map("likes")
}

model Comment {
  id        String    @id @default(cuid())
  content   String
  userId    String
  videoId   String
  parentId  String?
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  video     Video     @relation(fields: [videoId], references: [id], onDelete: Cascade)
  parent    Comment?  @relation(name: "CommentReplies", fields: [parentId], references: [id])
  replies   Comment[] @relation(name: "CommentReplies")
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  @@map("comments")
}

model Notification {
  id        String   @id @default(cuid())
  userId    String
  title     String
  message   String
  type      String   @default("info")
  read      Boolean  @default(false)
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}
