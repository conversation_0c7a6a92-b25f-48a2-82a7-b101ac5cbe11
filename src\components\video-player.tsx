
"use client"

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useUserStore } from '@/lib/store/useUserStore'
import { Video } from '@prisma/client'

interface VideoPlayerProps {
  video: Video
  isPurchased: boolean
  isSubscribed: boolean
}

export function VideoPlayer({ video, isPurchased, isSubscribed }: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [currentTime, setCurrentTime] = useState(0)
  const [showPayDialog, setShowPayDialog] = useState(false)
  const [playbackAllowed, setPlaybackAllowed] = useState(false)
  const { balance } = useUserStore()

  useEffect(() => {
    const videoElement = videoRef.current
    if (!videoElement) return

    const timeUpdateHandler = () => {
      setCurrentTime(videoElement.currentTime)
      
      if (videoElement.currentTime > 15 && !isPurchased && !isSubscribed) {
        videoElement.pause()
        setPlaybackAllowed(false)
        setShowPayDialog(true)
      }
    }

    videoElement.addEventListener('timeupdate', timeUpdateHandler)
    return () => videoElement.removeEventListener('timeupdate', timeUpdateHandler)
  }, [isPurchased, isSubscribed])

  const handlePlay = () => {
    if (isPurchased || isSubscribed) {
      setPlaybackAllowed(true)
      videoRef.current?.play()
    } else if (currentTime < 15) {
      setPlaybackAllowed(true)
      videoRef.current?.play()
    } else {
      setShowPayDialog(true)
    }
  }

  return (
    <div className="relative">
      <video
        ref={videoRef}
        src={video.url}
        className="w-full rounded-lg"
        controls={playbackAllowed}
        onPlay={handlePlay}
      />
      
      <Dialog open={showPayDialog} onOpenChange={setShowPayDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Unlock Full Video</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p>Watch the full video by purchasing access or subscribing to the creator.</p>
            
            <div className="flex gap-4">
              <Button 
                onClick={() => {
                  // Handle one-time purchase
                  if (balance >= video.price) {
                    // Implement purchase logic
                    setShowPayDialog(false)
                    setPlaybackAllowed(true)
                    videoRef.current?.play()
                  }
                }}
                disabled={balance < video.price}
              >
                Buy Once (Rp{video.price.toLocaleString()})
              </Button>
              
              <Button 
                variant="outline"
                onClick={() => {
                  // Handle subscription
                  if (balance >= 15999) {
                    // Implement subscription logic
                    setShowPayDialog(false)
                    setPlaybackAllowed(true)
                    videoRef.current?.play()
                  }
                }}
                disabled={balance < 15999}
              >
                Subscribe (Rp15,999/month)
              </Button>
            </div>
            
            {balance < Math.min(video.price, 15999) && (
              <p className="text-sm text-red-500">
                Insufficient balance. Please top up your account.
              </p>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}